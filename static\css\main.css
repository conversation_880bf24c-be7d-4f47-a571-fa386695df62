/* Taluk Office Professional CSS Framework */

:root {
    /* Official Government Color Palette */
    --primary-color: #003366;        /* Navy Blue - Official Government */
    --primary-dark: #002244;         /* Darker Navy */
    --primary-light: #004488;        /* Lighter Navy */
    --secondary-color: #8B0000;      /* Deep Red - Official Accent */
    --success-color: #006600;        /* Government Green */
    --warning-color: #CC6600;        /* Official Orange */
    --danger-color: #CC0000;         /* Official Red */
    --info-color: #004488;           /* Information Blue */
    --gold-color: #FFD700;           /* Official Gold */

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e8eaed;
    --gray-300: #dadce0;
    --gray-400: #9aa0a6;
    --gray-500: #5f6368;
    --gray-600: #3c4043;
    --gray-700: #202124;
    --gray-800: #1a1a1a;
    --gray-900: #000000;

    /* Official Gradients */
    --gradient-primary: linear-gradient(135deg, #003366 0%, #002244 100%);
    --gradient-secondary: linear-gradient(135deg, #8B0000 0%, #660000 100%);
    --gradient-success: linear-gradient(135deg, #006600 0%, #004400 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Government Header Styles */
.gov-header {
    background: linear-gradient(135deg, #ff9933 0%, #ffffff 50%, #138808 100%);
    border-bottom: 3px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 0.75rem 0;
}

.gov-emblem {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    width: 50px;
    height: 50px;
}

.gov-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 0.1rem;
}

.gov-title-secondary {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--secondary-color);
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 0.25rem;
}

.gov-subtitle {
    font-size: 0.9rem;
    color: var(--gray-700);
    font-weight: 500;
}

.gov-info {
    background: rgba(255, 255, 255, 0.8);
    padding: 0.4rem 0.8rem;
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 51, 102, 0.2);
    color: var(--gray-700);
}

/* Professional Government Navigation */
.modern-nav {
    background: var(--white) !important;
    border-bottom: 3px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
    z-index: 1000;
    padding: 0.5rem 0;
}

.modern-nav .navbar-nav {
    align-items: center;
}

.modern-nav .navbar-collapse {
    align-items: center;
}

.modern-nav .container-fluid {
    align-items: center;
}

.modern-nav .navbar-brand {
    margin-right: 2rem;
}

.modern-nav .navbar-nav.me-auto {
    flex-grow: 0;
}

.modern-nav .search-container {
    flex-shrink: 0;
}

.modern-nav .navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--primary-color) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
}

.modern-nav .nav-link {
    color: var(--gray-700) !important;
    font-weight: 500;
    padding: var(--spacing-3) var(--spacing-4) !important;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    margin: 0 var(--spacing-1);
    position: relative;
}

.modern-nav .nav-link:hover {
    background: var(--primary-color);
    color: var(--white) !important;
}

.modern-nav .nav-link.active {
    color: var(--white) !important;
    background: var(--primary-color);
}

.modern-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 350px;
    background: var(--white);
    border-radius: var(--radius-full);
    padding: 0.2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
}

.search-input {
    background: transparent;
    border: none;
    color: var(--gray-700);
    border-radius: var(--radius-full);
    padding: 0.5rem 1rem;
    padding-right: 45px;
    font-size: 0.9rem;
    width: 100%;
    font-weight: 500;
}

.search-input::placeholder {
    color: var(--gray-500);
}

.search-input:focus {
    outline: none;
    box-shadow: none;
}

.search-btn {
    position: absolute;
    right: 8px;
    background: var(--primary-color);
    border: none;
    color: var(--white);
    padding: 0.4rem;
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.search-btn:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: scale(1.05);
}

/* Voice Assistant States */
.search-btn.listening {
    background: #dc3545 !important;
    animation: pulse-listening 1.5s infinite;
}

.search-btn.speaking {
    background: #28a745 !important;
    animation: pulse-speaking 1s infinite;
}

@keyframes pulse-listening {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

@keyframes pulse-speaking {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* Voice indicator */
.voice-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 9999;
    display: none;
}

.voice-indicator.show {
    display: block;
    animation: fadeInSlide 0.3s ease-out;
}

@keyframes fadeInSlide {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Search Results Dropdown */
.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    margin-top: 5px;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.result-location {
    font-size: 0.85rem;
    color: #666;
}

.search-container {
    position: relative;
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    padding: 0.3rem 0.6rem !important;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    color: var(--primary-color) !important;
    font-weight: 600;
}

.user-menu:hover {
    background: var(--primary-color);
    color: var(--white) !important;
}

.user-avatar {
    width: 28px;
    height: 28px;
    background: var(--primary-color);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

/* Admin Avatar with different color */
.admin-avatar {
    background: #dc3545 !important; /* Red color for admin */
    color: white !important;
}

.admin-avatar:hover {
    background: #c82333 !important; /* Darker red on hover */
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 160px);
    padding-top: 0.5rem;
    background: var(--gray-50);
}

/* Modern Alerts */
.modern-alert {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--success-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--white);
}

/* Government Footer */
.gov-footer {
    background: var(--gray-800);
    color: var(--white);
    padding: 2rem 0 1rem;
    margin-top: auto;
    border-top: 3px solid var(--primary-color);
}

.footer-title {
    color: var(--primary-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.footer-text {
    color: var(--gray-300);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.footer-contact {
    color: var(--gray-400);
    font-size: 0.85rem;
    line-height: 1.6;
}

.footer-links {
    margin-bottom: 1rem;
}

.footer-link {
    color: var(--gray-300);
    text-decoration: none;
    margin: 0 0.5rem;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-light);
}

.footer-copyright {
    color: var(--gray-500);
    font-size: 0.8rem;
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-container {
        max-width: 250px;
        margin: 0.5rem 0;
        order: 1;
    }

    .modern-nav .navbar-nav.me-auto {
        order: 2;
    }

    .modern-nav .navbar-nav:last-child {
        order: 3;
    }

    .main-content {
        padding-top: 0.5rem;
    }

    .modern-nav .navbar-brand {
        font-size: var(--font-size-lg);
    }
}
