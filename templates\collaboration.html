{% extends "base.html" %}

{% block title %}Collaboration - T-Office{% endblock %}

{% block styles %}
<style>
.collaboration-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.collaboration-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.collaboration-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 1rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.collaboration-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.activity-panel {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.panel-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: between;
}

.panel-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.panel-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
}

.panel-body {
    padding: 0;
    max-height: 500px;
    overflow-y: auto;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--gray-50);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 45px;
    height: 45px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: var(--gray-800);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-meta {
    color: var(--gray-500);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--gray-400);
}

.online-users {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.users-header {
    background: var(--gradient-success);
    color: white;
    padding: 1.5rem 2rem;
}

.users-body {
    padding: 2rem;
}

.user-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.user-card {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.user-card:hover {
    background: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.user-card.online {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.user-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    position: relative;
}

.user-avatar-large.online::after {
    content: '';
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: var(--success-color);
    border: 3px solid white;
    border-radius: 50%;
}

.user-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.user-status {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.user-activity {
    font-size: 0.75rem;
    color: var(--gray-500);
}



.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.user-tracking-details {
    border-top: 1px solid var(--gray-200);
    padding-top: 0.5rem;
    font-size: 0.75rem;
}

.user-tracking-details div {
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.user-tracking-details i {
    width: 12px;
    text-align: center;
}

.real-time-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.collaboration-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
}

@media (max-width: 768px) {
    .collaboration-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .collaboration-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .collaboration-title {
        font-size: 2rem;
    }
    
    .user-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .user-card {
        padding: 1rem;
    }
    
    .user-avatar-large {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .activity-item {
        padding: 1rem;
    }
    
    .activity-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .panel-body {
        max-height: 300px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Collaboration Header -->
    <div class="collaboration-header">
        <h1 class="collaboration-title">
            <i class="fas fa-users me-3"></i>Real-time Collaboration
        </h1>
        <p class="collaboration-subtitle">See who's working with files in real-time and stay connected with your team.</p>

        <div class="d-flex justify-content-between align-items-center">
            <div class="status-indicator">
                <div class="status-dot"></div>
                Connected - Real-time updates active
            </div>

            {% if current_user.role == 'Administrator' or current_user.is_admin %}
            <a href="{{ url_for('manage_collaboration_access') }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-users-cog me-2"></i>Manage Access
            </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Online Users -->
    <div class="online-users">
        <div class="users-header">
            <h3 class="panel-title">
                <i class="fas fa-user-friends me-2"></i>Collaboration Users
                <span class="panel-badge ms-2" id="activeUserCount">{{ collaboration_users|length }} Users</span>
            </h3>
        </div>
        <div class="users-body">
            <div class="user-grid">
                <!-- Current User -->
                <div class="user-card online">
                    <div class="user-avatar-large online">
                        {{ current_user.username[0].upper() }}
                    </div>
                    <div class="user-name">{{ current_user.username }} (You)</div>
                    <div class="user-status">Online</div>
                    <div class="user-activity">Viewing collaboration</div>
                </div>

                <!-- Collaboration Users (user1, user2, user3) -->
                {% if is_admin %}
                    {% for user_data in user_tracking_data %}
                    <div class="user-card {{ user_data.status_class }}" data-user-id="{{ user_data.user.id }}">
                        <div class="user-avatar-large {{ user_data.status_class }}">
                            {{ user_data.user.username[0].upper() }}
                        </div>
                        <div class="user-name">{{ user_data.user.username }}</div>
                        <div class="user-status">{{ 'Online' if user_data.is_online else 'Offline' }}</div>
                        <div class="user-activity">{{ user_data.current_activity }}</div>

                        <!-- Admin-only tracking details -->
                        <div class="user-tracking-details mt-2">
                            <small class="text-muted">
                                <div><i class="fas fa-map-marker-alt"></i> {{ user_data.current_location }}</div>
                                {% if user_data.session_start %}
                                <div><i class="fas fa-clock"></i> Session: {{ user_data.session_start.strftime('%H:%M') }}</div>
                                {% endif %}
                                <div><i class="fas fa-file"></i> Files: {{ user_data.files_accessed }}</div>
                                <div><i class="fas fa-eye"></i> Pages: {{ user_data.pages_visited }}</div>
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <!-- Regular users see basic collaboration info -->
                    {% for user in collaboration_users %}
                    <div class="user-card offline">
                        <div class="user-avatar-large">
                            {{ user.username[0].upper() }}
                        </div>
                        <div class="user-name">{{ user.username }}</div>
                        <div class="user-status">Collaboration User</div>
                        <div class="user-activity">Available for collaboration</div>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Activity Grid -->
    {% if is_admin %}
    <div class="collaboration-grid">
        <!-- Real-time User Activities (Admin Only) -->
        <div class="activity-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <i class="fas fa-bolt me-2"></i>Real-time User Activities
                </h3>
                <span class="panel-badge" id="userActivityCount">{{ recent_activities|length or 0 }}</span>
            </div>
            <div class="panel-body">
                <ul class="activity-list" id="userActivityFeed">
                    {% for activity in recent_activities %}
                    <li class="activity-item">
                        <div class="activity-avatar">
                            {{ activity.user.username[0].upper() if activity.user else 'S' }}
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">
                                <strong>{{ activity.user.username if activity.user else 'System' }}</strong>
                                {{ activity.activity_description }}
                            </div>
                            <div class="activity-meta">
                                <div class="activity-time">
                                    <i class="fas fa-clock"></i>
                                    {{ activity.timestamp.strftime('%H:%M:%S') }}
                                </div>
                                <div class="activity-status status-online">
                                    <i class="fas fa-circle"></i>
                                    {{ activity.activity_type }}
                                </div>
                            </div>
                        </div>
                    </li>
                    {% else %}
                    <li class="activity-item">
                        <div class="activity-content">
                            <div class="activity-text text-muted">
                                <i class="fas fa-info-circle me-2"></i>No recent activities
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <!-- Live Activity Feed (Admin Only) -->
        <div class="activity-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <i class="fas fa-activity me-2"></i>File Access Log
                </h3>
                <span class="panel-badge" id="activityCount">{{ active_logs|length or 0 }}</span>
            </div>
            <div class="panel-body">
                <ul class="activity-list" id="activityFeed">
                    {% for log in active_logs %}
                    <li class="activity-item">
                        <div class="activity-avatar">
                            {{ log.user.username[0].upper() if log.user else 'S' }}
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">
                                {% if log.user %}{{ log.user.username }}{% else %}System{% endif %}
                                {{ log.action }}
                                {% if log.file %}"{{ log.file.title }}"{% endif %}
                            </div>
                            <div class="activity-meta">
                                <div class="activity-time">
                                    <i class="fas fa-clock"></i>
                                    {{ log.timestamp.strftime('%I:%M %p') }}
                                </div>
                                <div class="activity-status status-online">
                                    <i class="fas fa-circle"></i>
                                    Live
                                </div>
                            </div>
                        </div>
                    </li>
                    {% else %}
                    <li class="activity-item">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <p>No recent activity</p>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Regular User View -->
    <div class="collaboration-grid">
        <div class="activity-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <i class="fas fa-users me-2"></i>Collaboration Status
                </h3>
            </div>
            <div class="panel-body">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>Welcome to Collaboration</h4>
                    <p>You are connected to the collaboration system. Your activities are being tracked for administrative purposes.</p>
                    <div class="mt-3">
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            Connected and Active
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="activity-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <i class="fas fa-info-circle me-2"></i>Your Session
                </h3>
            </div>
            <div class="panel-body">
                <div class="p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong><i class="fas fa-user me-2"></i>Username:</strong>
                                <span class="text-muted">{{ current_user.username }}</span>
                            </div>
                            <div class="mb-3">
                                <strong><i class="fas fa-shield-alt me-2"></i>Role:</strong>
                                <span class="text-muted">{{ current_user.role }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong><i class="fas fa-clock me-2"></i>Session Time:</strong>
                                <span class="text-muted" id="sessionTime">Just now</span>
                            </div>
                            <div class="mb-3">
                                <strong><i class="fas fa-wifi me-2"></i>Status:</strong>
                                <span class="text-success">Online</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCollaboration();
    {% if not is_admin %}
    startSessionTimer();
    {% endif %}
});

let socket;
const isAdmin = {{ 'true' if is_admin else 'false' }};

function initializeCollaboration() {
    // Initialize Socket.IO for real-time updates
    if (typeof io !== 'undefined') {
        socket = io();

        // Connection events
        socket.on('connect', function() {
            console.log('Connected to collaboration server');

            // Emit user activity
            socket.emit('user_activity', {
                activity_type: 'collaboration_access',
                activity_description: 'Joined collaboration session',
                page_url: '/collaboration'
            });
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from collaboration server');
        });

        // Listen for user status updates
        socket.on('user_status_update', function(data) {
            updateUserStatus(data);
        });

        // Listen for real-time activities (admin only)
        if (isAdmin) {
            socket.on('real_time_activity', function(data) {
                addUserActivityToFeed(data);
            });

            socket.on('file_activity', function(data) {
                addActivityToFeed(data);
                showNotification(data);
            });
        }

        // Start periodic activity updates
        startActivityUpdates();
    }
}

function startActivityUpdates() {
    // Send periodic heartbeat to track user activity
    setInterval(function() {
        if (socket && socket.connected) {
            socket.emit('user_activity', {
                activity_type: 'heartbeat',
                activity_description: 'Active on collaboration page',
                page_url: '/collaboration'
            });
        }
    }, 30000); // Every 30 seconds

    // Refresh user tracking data every 60 seconds for admin
    if (isAdmin) {
        setInterval(function() {
            refreshUserTrackingData();
        }, 60000); // Every 60 seconds
    }
}

function refreshUserTrackingData() {
    // Fetch updated tracking data from server
    fetch('/collaboration')
        .then(response => response.text())
        .then(html => {
            // Parse the response to extract user tracking data
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newUserCards = doc.querySelectorAll('.user-card[data-user-id]');

            // Update existing user cards with new data
            newUserCards.forEach(newCard => {
                const userId = newCard.getAttribute('data-user-id');
                const existingCard = document.querySelector(`.user-card[data-user-id="${userId}"]`);

                if (existingCard) {
                    // Update status
                    const newStatus = newCard.querySelector('.user-status').textContent;
                    const newActivity = newCard.querySelector('.user-activity').textContent;
                    const newTrackingDetails = newCard.querySelector('.user-tracking-details');

                    existingCard.querySelector('.user-status').textContent = newStatus;
                    existingCard.querySelector('.user-activity').textContent = newActivity;

                    // Update online/offline classes
                    if (newCard.classList.contains('online')) {
                        existingCard.classList.add('online');
                        existingCard.querySelector('.user-avatar-large').classList.add('online');
                    } else {
                        existingCard.classList.remove('online');
                        existingCard.querySelector('.user-avatar-large').classList.remove('online');
                    }

                    // Update tracking details if admin
                    if (newTrackingDetails && isAdmin) {
                        const existingTrackingDetails = existingCard.querySelector('.user-tracking-details');
                        if (existingTrackingDetails) {
                            existingTrackingDetails.innerHTML = newTrackingDetails.innerHTML;
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.log('Error refreshing tracking data:', error);
        });
}

function addActivityToFeed(data) {
    const activityFeed = document.getElementById('activityFeed');
    const activityCount = document.getElementById('activityCount');
    
    // Create new activity item
    const activityItem = document.createElement('li');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-avatar">
            ${data.user[0].toUpperCase()}
        </div>
        <div class="activity-content">
            <div class="activity-text">
                <strong>${data.user}</strong> ${data.action} "${data.file_title}"
            </div>
            <div class="activity-meta">
                <div class="activity-time">
                    <i class="fas fa-clock"></i>
                    Just now
                </div>
                <div class="activity-status status-online">
                    <i class="fas fa-circle"></i>
                    Live
                </div>
            </div>
        </div>
    `;
    
    // Add animation
    activityItem.style.opacity = '0';
    activityItem.style.transform = 'translateX(-20px)';
    
    // Insert at the beginning
    const firstItem = activityFeed.querySelector('.activity-item');
    if (firstItem) {
        activityFeed.insertBefore(activityItem, firstItem);
    } else {
        activityFeed.appendChild(activityItem);
    }
    
    // Animate in
    setTimeout(() => {
        activityItem.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        activityItem.style.opacity = '1';
        activityItem.style.transform = 'translateX(0)';
    }, 100);
    
    // Update count
    const currentCount = parseInt(activityCount.textContent) || 0;
    activityCount.textContent = currentCount + 1;
    
    // Remove items if more than 10
    const items = activityFeed.querySelectorAll('.activity-item');
    if (items.length > 10) {
        items[items.length - 1].remove();
    }
}

function updateUserStatus(data) {
    // Update user status in the online users section
    const userCards = document.querySelectorAll('.user-card');
    userCards.forEach(card => {
        const userName = card.querySelector('.user-name').textContent.split(' (')[0];
        if (userName === data.username) {
            const statusElement = card.querySelector('.user-status');
            const activityElement = card.querySelector('.user-activity');
            const avatar = card.querySelector('.user-avatar-large');

            statusElement.textContent = data.status === 'online' ? 'Online' : 'Offline';

            if (data.status === 'online') {
                card.classList.add('online');
                avatar.classList.add('online');
            } else {
                card.classList.remove('online');
                avatar.classList.remove('online');
            }
        }
    });
}

function startSessionTimer() {
    const sessionTimeElement = document.getElementById('sessionTime');
    if (!sessionTimeElement) return;

    const startTime = new Date();

    setInterval(function() {
        const now = new Date();
        const diff = Math.floor((now - startTime) / 1000);

        const hours = Math.floor(diff / 3600);
        const minutes = Math.floor((diff % 3600) / 60);
        const seconds = diff % 60;

        let timeString = '';
        if (hours > 0) {
            timeString = `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            timeString = `${minutes}m ${seconds}s`;
        } else {
            timeString = `${seconds}s`;
        }

        sessionTimeElement.textContent = timeString;
    }, 1000);
}

function addNotification(data) {
    const notificationsBody = document.querySelector('.notifications-panel .panel-body');
    
    const notification = document.createElement('div');
    notification.className = 'notification-item unread';
    notification.innerHTML = `
        <div class="notification-icon ${data.type}">
            <i class="fas fa-${data.icon}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-text">${data.message}</div>
            <div class="notification-time">Just now</div>
        </div>
    `;
    
    // Insert at the beginning
    const firstNotification = notificationsBody.querySelector('.notification-item');
    if (firstNotification) {
        notificationsBody.insertBefore(notification, firstNotification);
    } else {
        notificationsBody.appendChild(notification);
    }
    
    // Update badge count
    const badge = document.querySelector('.notifications-header .panel-badge');
    const currentCount = parseInt(badge.textContent.split(' ')[0]) || 0;
    badge.textContent = `${currentCount + 1} New`;
}

function addUserActivityToFeed(data) {
    if (!isAdmin) return; // Only admin can see activity feeds

    const userActivityFeed = document.getElementById('userActivityFeed');
    const userActivityCount = document.getElementById('userActivityCount');

    if (!userActivityFeed || !userActivityCount) return;

    // Create new user activity item
    const activityItem = document.createElement('li');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-avatar">
            ${data.username[0].toUpperCase()}
        </div>
        <div class="activity-content">
            <div class="activity-text">
                <strong>${data.username}</strong> ${data.activity_description}
            </div>
            <div class="activity-meta">
                <div class="activity-time">
                    <i class="fas fa-clock"></i>
                    ${data.timestamp || 'Just now'}
                </div>
                <div class="activity-status status-online">
                    <i class="fas fa-circle"></i>
                    ${data.activity_type}
                </div>
            </div>
        </div>
    `;

    // Add to top of feed
    if (userActivityFeed.firstChild) {
        userActivityFeed.insertBefore(activityItem, userActivityFeed.firstChild);
    } else {
        userActivityFeed.appendChild(activityItem);
    }

    // Remove old items (keep only last 20)
    while (userActivityFeed.children.length > 20) {
        userActivityFeed.removeChild(userActivityFeed.lastChild);
    }

    // Update count
    const currentCount = parseInt(userActivityCount.textContent) || 0;
    userActivityCount.textContent = currentCount + 1;

    // Add animation
    activityItem.style.opacity = '0';
    activityItem.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        activityItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        activityItem.style.opacity = '1';
        activityItem.style.transform = 'translateY(0)';
    }, 100);
}

function addActivityToFeed(data) {
    if (!isAdmin) return; // Only admin can see activity feeds

    const activityFeed = document.getElementById('activityFeed');
    const activityCount = document.getElementById('activityCount');

    if (!activityFeed || !activityCount) return;

    // Create new activity item
    const activityItem = document.createElement('li');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-avatar">
            ${data.user[0].toUpperCase()}
        </div>
        <div class="activity-content">
            <div class="activity-text">
                <strong>${data.user}</strong> ${data.action} "${data.file_title}"
            </div>
            <div class="activity-meta">
                <div class="activity-time">
                    <i class="fas fa-clock"></i>
                    Just now
                </div>
                <div class="activity-status status-online">
                    <i class="fas fa-circle"></i>
                    Live
                </div>
            </div>
        </div>
    `;

    // Add animation
    activityItem.style.opacity = '0';
    activityItem.style.transform = 'translateX(-20px)';

    // Insert at the beginning
    const firstItem = activityFeed.querySelector('.activity-item');
    if (firstItem) {
        activityFeed.insertBefore(activityItem, firstItem);
    } else {
        activityFeed.appendChild(activityItem);
    }

    // Animate in
    setTimeout(() => {
        activityItem.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        activityItem.style.opacity = '1';
        activityItem.style.transform = 'translateX(0)';
    }, 100);

    // Update count
    const currentCount = parseInt(activityCount.textContent) || 0;
    activityCount.textContent = currentCount + 1;

    // Remove items if more than 10
    const items = activityFeed.querySelectorAll('.activity-item');
    if (items.length > 10) {
        items[items.length - 1].remove();
    }
}

function showNotification(data) {
    if (!isAdmin) return; // Only admin gets notifications

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
        new Notification(`T-Office: ${data.action}`, {
            body: `${data.user} ${data.action} "${data.file_title}"`,
            icon: '/static/images/icon-192x192.png',
            tag: 'toffice-activity'
        });
    }

    // Show in-app notification
    if (window.TOffice && window.TOffice.showNotification) {
        TOffice.showNotification(
            `${data.user} ${data.action} "${data.file_title}"`,
            'info',
            3000
        );
    }
}

// Request notification permission for admin users
if (isAdmin && 'Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (socket && socket.connected) {
        socket.emit('user_activity', {
            activity_type: 'page_leave',
            activity_description: 'Left collaboration page',
            page_url: '/collaboration'
        });
        socket.disconnect();
    }
});

// Real-time tracking updates are handled by Socket.IO events and periodic refresh

// Track collaboration page view
console.log('Collaboration page loaded for user: {{ current_user.username }}');
console.log('Admin access: ' + isAdmin);
</script>
{% endblock %}
